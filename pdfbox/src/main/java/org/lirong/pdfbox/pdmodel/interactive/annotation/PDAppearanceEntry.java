/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.lirong.pdfbox.pdmodel.interactive.annotation;

import java.util.HashMap;
import java.util.Map;
import org.lirong.pdfbox.cos.COSDictionary;
import org.lirong.pdfbox.cos.COSName;
import org.lirong.pdfbox.cos.COSStream;
import org.lirong.pdfbox.pdmodel.common.COSDictionaryMap;
import org.lirong.pdfbox.pdmodel.common.COSObjectable;

/**
 * An entry in an appearance dictionary. May contain either a single appearance stream or an appearance subdictionary.
 *
 * <AUTHOR>
 */
public class PDAppearanceEntry implements COSObjectable
{
    private COSDictionary entry;

    private PDAppearanceEntry()
    {
    }

    /**
     * Constructor for reading.
     * 
     * @param entry the dictionary of the appearance entry
     */
    public PDAppearanceEntry(COSDictionary entry)
    {
        this.entry = entry;
    }

    @Override
    public COSDictionary getCOSObject()
    {
        return entry;
    }

    /**
     * Returns true if this entry is an appearance subdictionary.
     * 
     * @return true if this entry is an appearance subdictionary
     */
    public boolean isSubDictionary()
    {
        return !(this.entry instanceof COSStream);
    }

    /**
     * Returns true if this entry is an appearance stream.
     * 
     * @return true if this entry is an appearance stream
     */
    public boolean isStream()
    {
        return this.entry instanceof COSStream;
    }

    /**
     * Returns the entry as an appearance stream.
     *
     * @return the entry as an appearance stream
     * 
     * @throws IllegalStateException if this entry is not an appearance stream
     */
    public PDAppearanceStream getAppearanceStream()
    {
        if (!isStream())
        {
            throw new IllegalStateException("This entry is not an appearance stream");
        }
        return new PDAppearanceStream((COSStream) entry);
    }

    /**
     * Returns the entry as an appearance subdictionary.
     *
     * @return the entry as an appearance subdictionary
     * 
     * @throws IllegalStateException if this entry is not an appearance subdictionary
     */
    public Map<COSName, PDAppearanceStream> getSubDictionary()
    {
        if (!isSubDictionary())
        {
            throw new IllegalStateException("This entry is not an appearance subdictionary");
        }

        COSDictionary dict = entry;
        Map<COSName, PDAppearanceStream> map = new HashMap<>();

        for (COSName name : dict.keySet())
        {
            COSStream stream = dict.getCOSStream(name);
            // the file from PDFBOX-1599 contains /null as its entry, so we skip non-stream entries
            if (stream != null)
            {
                map.put(name, new PDAppearanceStream(stream));
            }
        }
        return new COSDictionaryMap<>(map, dict);
    }
}
