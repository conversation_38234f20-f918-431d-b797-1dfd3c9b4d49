/*
 * Copyright 2015 The Apache Software Foundation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.lirong.pdfbox.pdmodel.font;

import java.util.Locale;

/**
 * Utility class for Unicode fallback.
 *
 * <AUTHOR>
 */
final class UniUtil
{
    private UniUtil()
    {
    }

    // faster than String.format("uni%04X", codePoint)
    static String getUniNameOfCodePoint(int codePoint)
    {
        String hex = Integer.toString(codePoint, 16).toUpperCase(Locale.US);
        switch (hex.length())
        {
            case 1:
                return "uni000" + hex;
            case 2:
                return "uni00" + hex;
            case 3:
                return "uni0" + hex;
            default:
                return "uni" + hex;
        }
    }
}
