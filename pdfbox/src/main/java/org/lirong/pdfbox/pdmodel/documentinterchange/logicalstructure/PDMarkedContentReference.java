/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.lirong.pdfbox.pdmodel.documentinterchange.logicalstructure;

import org.lirong.pdfbox.cos.COSDictionary;
import org.lirong.pdfbox.cos.COSName;
import org.lirong.pdfbox.pdmodel.PDPage;
import org.lirong.pdfbox.pdmodel.common.COSObjectable;

/**
 * A marked-content reference.
 * 
 * <AUTHOR>
 */
public class PDMarkedContentReference implements COSObjectable
{
    public static final String TYPE = "MCR";

    private final COSDictionary dictionary;

    /**
     * Default constructor
     */
    public PDMarkedContentReference()
    {
        this.dictionary = new COSDictionary();
        this.dictionary.setName(COSName.TYPE, TYPE);
    }

    /**
     * Constructor for an existing marked content reference.
     * 
     * @param dictionary the page dictionary
     */
    public PDMarkedContentReference(COSDictionary dictionary)
    {
        this.dictionary = dictionary;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public COSDictionary getCOSObject()
    {
        return this.dictionary;
    }

    /**
     * Gets the page.
     * 
     * @return the page
     */
    public PDPage getPage()
    {
        COSDictionary pg = getCOSObject().getCOSDictionary(COSName.PG);
        return pg != null ? new PDPage(pg) : null;
    }

    /**
     * Sets the page.
     * 
     * @param page the page
     */
    public void setPage(PDPage page)
    {
        this.getCOSObject().setItem(COSName.PG, page);
    }

    /**
     * Gets the marked content identifier.
     * 
     * @return the marked content identifier
     */
    public int getMCID()
    {
        return this.getCOSObject().getInt(COSName.MCID);
    }

    /**
     * Sets the marked content identifier.
     * 
     * @param mcid the marked content identifier
     */
    public void setMCID(int mcid)
    {
        this.getCOSObject().setInt(COSName.MCID, mcid);
    }


    @Override
    public String toString()
    {
        return "mcid=" + this.getMCID();
    }

}
