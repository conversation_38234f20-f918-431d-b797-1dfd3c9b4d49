package com.lirong.framework.vectorstore.config;

import com.lirong.framework.vectorstore.props.VectorStoreProperties;
import dev.langchain4j.store.embedding.pgvector.PgVectorEmbeddingStore;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

@AutoConfiguration(after = VectorStoreProperties.class)
@AllArgsConstructor
@EnableConfigurationProperties(VectorStoreProperties.class)
public class VectorStoreConfiguration {

    @Bean(name = "pgVectorEmbeddingStore")
    public PgVectorEmbeddingStore pgVectorEmbeddingStore(VectorStoreProperties properties) {
        return PgVectorEmbeddingStore.builder()
                .host(properties.getHost())
                .port(properties.getPort())
                .database(properties.getDatabase())
                .dimension(properties.getDimension())
                .user(properties.getUser())
                .password(properties.getPassword())
                .table(properties.getTable())
                .useIndex(properties.getUseIndex())
                .createTable(properties.getCreateTable())
                .dropTableFirst(properties.getDropTableFirst())
                .build();
    }
}
