package com.lirong;

import dev.langchain4j.chain.ConversationalRetrievalChain;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.allminilml6v2.AllMiniLmL6V2EmbeddingModel;
import dev.langchain4j.retriever.EmbeddingStoreRetriever;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.elasticsearch.ElasticsearchEmbeddingStore;
import dev.langchain4j.store.embedding.pgvector.PgVectorEmbeddingStore;

import java.util.List;

public class ChatDocument {

    public static void main(String[] args) {

//        EmbeddingStore<TextSegment> embeddingStore = ElasticsearchEmbeddingStore.builder()
//                .serverUrl("http://*************:9200")
//                .dimension(384)
//                .build();
//        PgVectorEmbeddingStore.builder().host("*************").port(5432).database("langchat")
//                .
//                .build();

        /**
         *       host: *************
         *       user: fastgpt
         *       password: password
         *       port: 5432
         *       database: langchat
         *       table: vector_2
         *       dimension: 384
         */

        EmbeddingStore<TextSegment> embeddingStore = PgVectorEmbeddingStore.builder()
                .host("*************")
                .port(5432)
                .database("langchat")
                .dimension(384)
                .user("fastgpt")
                .password("password")
                .table("vector_2")
//                .useIndex(properties.getUseIndex())
                .createTable(true)
                .dropTableFirst(false)
                .build();
        EmbeddingModel embeddingModel = new AllMiniLmL6V2EmbeddingModel();

        Embedding queryEmbedding = embeddingModel.embed("Department of Defense").content();
        List<EmbeddingMatch<TextSegment>> relevant = embeddingStore.findRelevant(queryEmbedding, 20);
        relevant.forEach(e -> {
            System.out.println(e.score()); // 0.8144289
            System.out.println(e.embedded().text()); // I like football.
            System.out.println("===========");
        });
        System.out.println("===== end ======");
//        EmbeddingMatch<TextSegment> embeddingMatch = relevant.get(0);
//
//        System.out.println(embeddingMatch.score()); // 0.8144289
//        System.out.println(embeddingMatch.embedded().text()); // I like football.

//        ConversationalRetrievalChain chain = ConversationalRetrievalChain.builder()
//                .chatLanguageModel(OpenAiChatModel.withApiKey(ApiKeys.OPENAI_API_KEY))
//                .retriever(EmbeddingStoreRetriever.from(embeddingStore, embeddingModel))
//                // .chatMemory() // you can override default chat memory
//                // .promptTemplate() // you can override default prompt template
//                .build();
//
//        String answer = chain.execute("Who is Charlie?");
//        System.out.println(answer); // Charlie is a cheerful carrot living in VeggieVille...

    }

}
