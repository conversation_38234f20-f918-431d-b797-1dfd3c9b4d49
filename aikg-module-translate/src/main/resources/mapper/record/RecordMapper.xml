<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.module.translate.dal.mysql.record.RecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <resultMap id="recordResultMap" type="com.lirong.module.translate.dal.dataobject.record.RecordDO">
        <id column="id" property="id" />
        <result column="source" property="source" />
        <result column="target" property="target" />
        <result column="source_language" property="sourceLanguage" />
        <result column="target_language" property="targetLanguage" />
        <result column="file_name" property="fileName" />
        <result column="status" property="status" />
        <result column="percent" property="percent" />

    </resultMap>

    <select id="selectTranslatedRecords" resultMap="recordResultMap">
        select distinct "source", target, source_language, target_language, file_name, md5, percent, status
        from trans_record
        where status in (1, 2)
    </select>
</mapper>