package com.lirong.module.translate.service.record;

import javax.validation.*;
import com.lirong.module.translate.controller.admin.record.vo.*;
import com.lirong.module.translate.dal.dataobject.record.RecordDO;
import com.lirong.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 翻译记录 Service 接口
 *
 * <AUTHOR>
 */
public interface TranslatedRecordService {

    /**
     * 创建翻译记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRecord(@Valid RecordSaveReqVO createReqVO);

    /**
     * 更新翻译记录
     *
     * @param updateReqVO 更新信息
     */
    void updateRecord(@Valid RecordSaveReqVO updateReqVO);

    /**
     * 同步更新相同文件的翻译进度
     * @param recordDO
     */
    void updateCompleteRecord(RecordDO recordDO);

    /**
     * 删除翻译记录
     *
     * @param id 编号
     */
    void deleteRecord(Long id);

    /**
     * 获得翻译记录
     *
     * @param id 编号
     * @return 翻译记录
     */
    RecordDO getRecord(Long id);


    /**
     * 根据文献ID获取翻译记录
     * @param docId
     * @return
     */
    RecordDO getRecordByDocId(Long docId);

    /**
     * 获得翻译记录分页
     *
     * @param pageReqVO 分页查询
     * @return 翻译记录分页
     */
    PageResult<RecordDO> getRecordPage(RecordPageReqVO pageReqVO);

    /**
     * 根据md5获取翻译记录
     * @param md5
     * @return
     */
    RecordDO getRecordByMD5(String md5);

    /**
     * 根据md5和语言获取翻译记录
     * @param md5
     * @param lang
     * @return
     */
    RecordDO getRecordByMD5AndLang(String md5,String lang);

    /**
     * 获取待翻译记录
     * @return
     */
    List<RecordDO> getUnTranslatedRecords();

    List<RecordDO> getTranslatedRecords(RecordPageReqVO pageReqVO);

}