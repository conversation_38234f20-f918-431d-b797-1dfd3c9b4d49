package com.lirong.module.translate.translator;

import com.lirong.framework.common.util.spring.SpringUtils;
import dev.langchain4j.model.localai.LocalAiChatModel;
import dev.langchain4j.model.localai.LocalAiChatModel.LocalAiChatModelBuilder;

import java.util.List;

public class LocalAiTranslator extends Translator {

    private LocalAiChatModel.LocalAiChatModelBuilder localAiChatModelBuilder;

    public LocalAiTranslator(List<Glossary> glossaries) {
        initModel(glossaries);
    }

    public LocalAiTranslator() {
        this(null);
    }

    @Override
    public void initModel(List<Glossary> glossaries) {
//        model = LocalAiChatModel.builder().baseUrl("http://192.168.0.222:8000/v1/").modelName("glm-4").build();

        LocalAiChatModelBuilder builder = SpringUtils.getBean(LocalAiChatModelBuilder.class);
        model = builder.build();

        glossary = new StringBuilder();
        if (glossaries != null) {
            glossaries.forEach(item -> {
                glossary.append(item.getKey()).append(" : ").append(item.getValue()).append("\n");
            });
        }
    }

}
