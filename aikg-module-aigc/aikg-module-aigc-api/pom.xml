<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lirong.boot</groupId>
        <artifactId>aikg-module-aigc</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>aikg-module-aigc-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-common</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>