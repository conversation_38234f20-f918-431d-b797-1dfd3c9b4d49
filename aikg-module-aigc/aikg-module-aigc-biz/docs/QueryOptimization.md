# 知识库查询优化功能

## 概述

本功能对 `chatKnowledge` 方法进行了优化，通过AI模型对用户查询进行预处理和优化，提高语义检索的准确率。

## 主要特性

### 1. 智能语言检测
- 自动检测用户查询的主要语言（中文、英文、其他）
- 支持混合语言文本的主要语言识别

### 2. 查询优化
- 使用AI模型对查询进行语义扩展
- 为中文查询添加相关英文关键词
- 为英文查询添加相关中文关键词
- 扩展同义词和相关概念

### 3. 翻译增强
- 对中文查询自动生成英文翻译作为补充
- 提高跨语言文档的检索效果

## 实现架构

### 核心组件

1. **QueryOptimizer 接口**
   - 定义查询优化的核心方法
   - 包括语言检测、查询优化、文本翻译

2. **QueryOptimizerImpl 实现类**
   - 实现具体的优化逻辑
   - 集成AI模型进行智能处理

3. **QueryOptimizerAI 服务接口**
   - 定义AI模型调用的方法
   - 使用 LangChain4j 框架

4. **OptimizedQueryTransformer**
   - 自定义查询转换器
   - 在检索过程中使用优化后的查询

## 使用流程

1. **用户提交查询** → 原始查询文本
2. **语言检测** → 识别查询语言类型
3. **查询优化** → AI模型优化查询文本
4. **翻译增强** → 为中文查询添加英文翻译
5. **语义检索** → 使用优化后的查询进行向量检索
6. **结果返回** → 基于优化检索的问答结果

## 配置说明

### 依赖注入
```java
@Service
public class LangDocServiceImpl implements LangDocService {
    private final QueryOptimizer queryOptimizer;
    
    public LangDocServiceImpl(..., QueryOptimizer queryOptimizer) {
        this.queryOptimizer = queryOptimizer;
    }
}
```

### 模型配置
- 使用 LocalAI 模型进行查询优化
- 温度设置：0.5（平衡创造性和准确性）
- TopP 设置：0.8

## 示例

### 输入查询
```
原始查询: "机器学习算法"
```

### 优化过程
```
1. 语言检测: zh (中文)
2. 查询优化: "机器学习算法 深度学习 神经网络 人工智能算法"
3. 翻译增强: "machine learning algorithms deep learning neural networks"
4. 最终查询: "机器学习算法 深度学习 神经网络 人工智能算法 machine learning algorithms deep learning neural networks"
```

### 检索效果
- 提高了相关文档的召回率
- 增强了跨语言文档的检索能力
- 改善了问答的准确性

## 性能考虑

### 优化策略
- 异常处理：优化失败时回退到原始查询
- 缓存机制：可考虑对常见查询进行缓存
- 并发控制：支持多用户并发查询优化

### 监控指标
- 查询优化成功率
- 平均优化时间
- 检索结果质量提升

## 扩展功能

### 未来改进方向
1. **查询缓存**：缓存常见查询的优化结果
2. **个性化优化**：基于用户历史进行个性化查询优化
3. **多模态支持**：支持图片、音频等多模态查询
4. **实时学习**：基于用户反馈持续优化查询策略

## 故障排除

### 常见问题
1. **优化失败**：自动回退到原始查询，不影响基本功能
2. **性能问题**：可通过调整模型参数或添加缓存解决
3. **翻译质量**：可通过优化提示词模板改善

### 日志监控
```
INFO: 开始优化用户查询: 机器学习
INFO: 查询优化完成: 机器学习 -> 机器学习算法 深度学习 neural networks
ERROR: 查询优化失败，使用原始查询: 机器学习
```
