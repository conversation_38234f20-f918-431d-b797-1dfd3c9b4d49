package com.lirong.module.aigc.service.message;

import java.util.*;
import javax.validation.*;
import com.lirong.module.aigc.controller.admin.message.vo.*;
import com.lirong.module.aigc.dal.dataobject.message.MessageDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.pojo.PageParam;

/**
 * 对话消息 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageService {

    /**
     * 创建对话消息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMessage(@Valid MessageSaveReqVO createReqVO);

    /**
     * 更新对话消息
     *
     * @param updateReqVO 更新信息
     */
    void updateMessage(@Valid MessageSaveReqVO updateReqVO);

    /**
     * 删除对话消息
     *
     * @param id 编号
     */
    void deleteMessage(Long id);

    /**
     * 根据会话删除消息
     * @param conversationId
     */
    int deleteMessageByConversation(Long conversationId);

    /**
     * 获得对话消息
     *
     * @param id 编号
     * @return 对话消息
     */
    MessageDO getMessage(Long id);

    /**
     * 获得对话消息分页
     *
     * @param pageReqVO 分页查询
     * @return 对话消息分页
     */
    PageResult<MessageDO> getMessagePage(MessagePageReqVO pageReqVO);

    /**
     * 获取会话信息
     * @param conversationId
     * @return
     */
    List<MessageDO> getMessageByConversion(Long conversationId);

}