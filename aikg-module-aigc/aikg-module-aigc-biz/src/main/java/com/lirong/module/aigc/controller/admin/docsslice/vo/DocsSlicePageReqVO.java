package com.lirong.module.aigc.controller.admin.docsslice.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.lirong.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.lirong.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 文档切片分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DocsSlicePageReqVO extends PageParam {

    @Schema(description = "向量库的ID", example = "25506")
    private String vectorId;

    @Schema(description = "文档ID", example = "7517")
    private Long docsId;

    @Schema(description = "知识库ID", example = "6314")
    private Long knowledgeId;

    @Schema(description = "文档名称", example = "赵六")
    private String name;

    @Schema(description = "切片内容")
    private String content;

    @Schema(description = "字符数")
    private Integer wordNum;

    @Schema(description = "状态", example = "2")
    private Boolean status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}