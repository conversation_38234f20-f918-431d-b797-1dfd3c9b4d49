package com.lirong.module.aigc.core.service.impl;

import com.lirong.framework.common.util.spring.SpringUtils;
import com.lirong.module.aigc.core.dto.ChatReq;
import com.lirong.module.aigc.core.dto.EmbeddingR;
import com.lirong.module.aigc.core.provider.EmbedProvider;
import com.lirong.module.aigc.core.provider.MemoryStoreProvider;
import com.lirong.module.aigc.core.rag.CustomRetrievalAugmentor;
import com.lirong.module.aigc.core.rag.OptimizedQueryTransformer;
import com.lirong.module.aigc.core.service.AiStream;
import com.lirong.module.aigc.core.service.Assistant;
import com.lirong.module.aigc.core.service.LangDocService;
import com.lirong.module.aigc.core.service.QueryOptimizer;
import com.lirong.module.aigc.service.converter.ExcelToMarkdownConverter;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.loader.FileSystemDocumentLoader;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.loader.UrlDocumentLoader;
import dev.langchain4j.data.document.parser.apache.tika.ApacheTikaDocumentParser;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.bge.BgeRerankModel;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.localai.LocalAiChatModel;
import dev.langchain4j.model.localai.LocalAiStreamingChatModel;
import dev.langchain4j.rag.DefaultRetrievalAugmentor;
import dev.langchain4j.rag.RetrievalAugmentor;
import dev.langchain4j.rag.content.aggregator.ContentAggregator;
import dev.langchain4j.rag.content.aggregator.ReRankingContentAggregator;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.rag.query.transformer.CompressingQueryTransformer;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.TokenStream;
import dev.langchain4j.store.embedding.filter.Filter;
import dev.langchain4j.store.embedding.pgvector.PgVectorEmbeddingStore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.lirong.module.aigc.core.consts.EmbedConst.*;
import static dev.langchain4j.data.document.Metadata.metadata;
import static dev.langchain4j.store.embedding.filter.MetadataFilterBuilder.metadataKey;

@Slf4j
@Service
public class LangDocServiceImpl implements LangDocService {

    private final EmbedProvider embedProvider;
    private final PgVectorEmbeddingStore embeddingStore;
    private final MemoryStoreProvider memoryStoreProvider;
    private final Lock lock = new ReentrantLock();
    private final BgeRerankModel bgeRerankModel;
    private final QueryOptimizer queryOptimizer;

    public LangDocServiceImpl(EmbedProvider embedProvider,
                             @Qualifier("pgVectorEmbeddingStore") PgVectorEmbeddingStore embeddingStore,
                             MemoryStoreProvider memoryStoreProvider,
                             BgeRerankModel bgeRerankModel,
                             QueryOptimizer queryOptimizer) {
        this.embedProvider = embedProvider;
        this.embeddingStore = embeddingStore;
        this.memoryStoreProvider = memoryStoreProvider;
        this.bgeRerankModel = bgeRerankModel;
        this.queryOptimizer = queryOptimizer;
    }

    @Override
    public EmbeddingR embeddingText(ChatReq req) {
        TextSegment segment = TextSegment.from(req.getMessage(),
                metadata(KNOWLEDGE, req.getKnowledgeId())
                    .put(FILENAME, req.getDocsName())
                    .put(DOC_ID, req.getDocsId())
                    .put(PARENT_ID, req.getParentId())
                    .put(USER_ID, req.getUserId()));
        EmbeddingModel embeddingModel = embedProvider.embed();;
        Embedding embedding = embeddingModel.embed(segment).content();

        String id = embeddingStore.add(embedding, segment);
        return new EmbeddingR().setVectorId(id).setText(segment.text());
    }

    /**
     * 从指定的URL下载文件到一个临时文件，并返回该临时文件
     *
     * @param fileURL 文件的URL地址
     * @return 下载后的临时文件
     * @throws IOException 如果发生I/O错误
     */
    public File downloadFileToTemp(String fileURL) throws IOException {
        // 创建URL对象
        URL url = new URL(fileURL);
        // 打开连接
        URLConnection connection = url.openConnection();

        // 连接设置
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3");

        // 获取文件名（包括扩展名）
        String fileName = "";
        String path = url.getPath();
        int startIndex = path.lastIndexOf('/') + 1;
        if (startIndex > 0) {
            fileName = path.substring(startIndex);
        }

        // 创建临时文件
        File tempFile = File.createTempFile("downloaded", fileName);
//        tempFile.deleteOnExit(); // 在JVM退出时删除临时文件

        // 使用BufferedInputStream和BufferedOutputStream来读取并写入文件
        try (BufferedInputStream bis = new BufferedInputStream(connection.getInputStream());
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(tempFile))) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            // 读取数据并写入文件
            while ((bytesRead = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
        }
        // 返回临时文件
        return tempFile;
    }

    public static String removePdfOriginalContent(String input) {
        // 正则表达式匹配 << /ASCII85EncodePages 开始，到 >> setpagedevice 结束的内容
        String regex = "(?s)<<\\s*/ASCII85EncodePages.*?>>\\s*setpagedevice";
        // 使用正则表达式删除匹配的内容
        return input.replaceAll(regex, "");
    }

    @Override
    public List<EmbeddingR> embeddingDocs(ChatReq req) {
        List<EmbeddingR> list = new ArrayList<>();
        Document document = null;
        if (req.getUrl().startsWith("http")) {
            if (req.getUrl().endsWith(".xls") || req.getUrl().endsWith(".xlsx") || req.getUrl().endsWith(".csv") || req.getUrl().endsWith(".xlsm")) {
                try {
                    String content = ExcelToMarkdownConverter.convertToMarkdown(req.getUrl());
                    document = new Document(content);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                document = UrlDocumentLoader.load(req.getUrl(), new ApacheTikaDocumentParser());
                if (document.text().contains("/ASCII85EncodePages")) {
                    document = new Document(removePdfOriginalContent(document.text()));
                }
            }
        } else {
            document = FileSystemDocumentLoader.loadDocument(req.getUrl(), new ApacheTikaDocumentParser());
        }
        document.metadata().put(KNOWLEDGE, req.getKnowledgeId())
                .put(FILENAME, req.getDocsName())
                .put(DOC_ID, req.getDocsId())
                .put(PARENT_ID, req.getParentId())
                .put(USER_ID, req.getUserId());

        EmbeddingModel model = embedProvider.embed();
        DocumentSplitter splitter = EmbedProvider.splitter();
        List<TextSegment> segments = splitter.split(document);
        lock.lock();
        try {
            List<Embedding> embeddings = model.embedAll(segments).content();
            List<String> ids = embeddingStore.addAll(embeddings, segments);

            for (int i = 0; i < ids.size(); i++) {
                list.add(new EmbeddingR().setVectorId(ids.get(i)).setText(segments.get(i).text()));
            }
        } finally {
            lock.unlock();
        }
        return list;
    }

    public AiStream chatKnowledge(ChatReq req) {
//        LocalAiChatModel.LocalAiChatModelBuilder builder = SpringUtils.getBean(LocalAiChatModel.LocalAiChatModelBuilder.class);
        LocalAiStreamingChatModel.LocalAiStreamingChatModelBuilder streamingBuilder = SpringUtils.getBean(LocalAiStreamingChatModel.LocalAiStreamingChatModelBuilder.class);

        StreamingChatLanguageModel streamingChatModel = streamingBuilder.temperature(0.7).topP(0.8).build();
//        ChatLanguageModel chatModel = builder.temperature(0.7).topP(0.8).build();
        ChatMemoryProvider chatMemoryProvider = memoryId -> MessageWindowChatMemory
                .builder()
                .id(memoryId)
                .maxMessages(4)
                .chatMemoryStore(memoryStoreProvider.memoryStore())
                .build();

        // 查询优化：使用模型对用户查询进行优化，提高语义检索准确率
        String originalMessage = req.getMessage();
        String optimizedMessage = originalMessage;

        try {
            log.info("开始优化用户查询: {}", originalMessage);
            optimizedMessage = queryOptimizer.optimizeQuery(originalMessage);
            log.info("查询优化完成: {} -> {}", originalMessage, optimizedMessage);
        } catch (Exception e) {
            log.error("查询优化失败，使用原始查询: {}", originalMessage, e);
        }

        AiServices<Assistant> aiServices = AiServices.builder(Assistant.class)
                .chatMemoryProvider(chatMemoryProvider)
                .streamingChatLanguageModel(streamingChatModel);

        AiStream aiStream = AiStream.builder().build();
        EmbeddingModel model = embedProvider.embed();

        Filter filterByUserId = metadataKey(USER_ID).isEqualTo(req.getUserId());

        if (req.getKnowledgeId() != null) {
            filterByUserId = filterByUserId.and(metadataKey(KNOWLEDGE).isEqualTo(req.getKnowledgeId()));
        }

        // 使用优化后的查询创建内容检索器
        ContentRetriever contentRetriever = EmbeddingStoreContentRetriever.builder()
                .embeddingStore(embeddingStore)
                .embeddingModel(model)
                .maxResults(80)
                .minScore(0.6)
                .filter(filterByUserId)
                .build();

//        ContentAggregator contentAggregator = ReRankingContentAggregator.builder()
//                .scoringModel(bgeRerankModel)
//                .minScore(0.9)
//                .maxResults(5)
//                .build();

        // 使用优化查询转换器和压缩查询转换器的组合
        RetrievalAugmentor retrievalAugmentor = DefaultRetrievalAugmentor.builder()
                .contentRetriever(contentRetriever)
                .queryTransformer(new OptimizedQueryTransformer(optimizedMessage))
//                .contentAggregator(contentAggregator)
                .build();

        aiServices.retrievalAugmentor(retrievalAugmentor);

        Assistant assistant = aiServices.build();
        // 使用原始查询进行对话，但检索时使用优化后的查询
        TokenStream tokenStream = assistant.stream(req.getConversationId().toString(), originalMessage);
        aiStream.setTokenStream(tokenStream);
        return aiStream;
    }

    public AiStream chatDoc(ChatReq req) {
        LocalAiStreamingChatModel.LocalAiStreamingChatModelBuilder streamingBuilder
                = SpringUtils.getBean(LocalAiStreamingChatModel.LocalAiStreamingChatModelBuilder.class);
        StreamingChatLanguageModel streamingChatModel
                = streamingBuilder.temperature(0.7).topP(0.8).build();

        ChatMemoryProvider chatMemoryProvider = memoryId -> MessageWindowChatMemory
                .builder()
                .id(memoryId)
                .maxMessages(5)
                .chatMemoryStore(memoryStoreProvider.memoryStore())
                .build();

        AiServices<Assistant> aiServices = AiServices.builder(Assistant.class)
                .chatMemoryProvider(chatMemoryProvider)
                .streamingChatLanguageModel(streamingChatModel);

        AiStream aiStream = AiStream.builder().build();
        EmbeddingModel model = embedProvider.embed();

//        Function<Query, Filter> filterByUserId = (query) -> metadataKey(DOC_ID).isEqualTo(req.getDocsId());
        Filter filterByDocId = metadataKey(DOC_ID).isEqualTo(req.getDocsId());

        ContentRetriever contentRetriever = EmbeddingStoreContentRetriever.builder()
                .filter(filterByDocId)
                .embeddingStore(embeddingStore)
                .embeddingModel(model)
                .maxResults(5)
                .build();

        ContentAggregator contentAggregator = ReRankingContentAggregator.builder()
                .scoringModel(bgeRerankModel)
                .minScore(0.8)
                .maxResults(5)
                .build();

        RetrievalAugmentor retrievalAugmentor = DefaultRetrievalAugmentor.builder()
                .contentRetriever(contentRetriever)
//                .contentAggregator(contentAggregator)
                .build();

//        aiServices.contentRetriever(contentRetriever);
        aiServices.retrievalAugmentor(retrievalAugmentor);


//        aiStream.setSources(contentRetriever.retrieve(new Query(req.getMessage())));

//        ChatMemory chatMemory = MessageWindowChatMemory.withMaxMessages(10);
//        aiServices.chatMemory(chatMemory);

        Assistant assistant = aiServices.build();

        TokenStream tokenStream = assistant.stream(req.getConversationId().toString(), req.getMessage());
        aiStream.setTokenStream(tokenStream);
        return aiStream;
    }


}
