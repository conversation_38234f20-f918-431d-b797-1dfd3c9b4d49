package com.lirong.module.aigc.dal.dataobject.knowledge;

import com.lirong.framework.tenant.core.db.TenantBaseDO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.StringSerializer;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 知识库 DO
 *
 * <AUTHOR>
 */
@TableName("aigc_knowledge")
@KeySequence("aigc_knowledge_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = StringSerializer.class)
    private Long id;
    /**
     * 知识库名称
     */
    private String name;
    /**
     * 描述
     */
    private String des;
    /**
     * 封面
     */
    private String cover;
    /**
     * 是否结构化数据
     *
     * 枚举 {@link TODO infra_boolean_string 对应的类}
     */
    private Boolean isExcel;

    /**
     * 文档数量
     */
    @TableField(exist = false)
    private Long docNum;

}