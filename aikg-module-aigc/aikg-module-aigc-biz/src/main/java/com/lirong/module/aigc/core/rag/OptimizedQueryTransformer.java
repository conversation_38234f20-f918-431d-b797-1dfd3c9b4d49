package com.lirong.module.aigc.core.rag;

import dev.langchain4j.rag.query.Query;
import dev.langchain4j.rag.query.transformer.QueryTransformer;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Collections;

/**
 * 优化的查询转换器
 * 使用预先优化的查询文本替换原始查询
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
public class OptimizedQueryTransformer implements QueryTransformer {
    
    private final String optimizedQuery;
    
    public OptimizedQueryTransformer(String optimizedQuery) {
        this.optimizedQuery = optimizedQuery;
    }
    
    @Override
    public Collection<Query> transform(Query query) {
        try {
            log.info("使用优化查询替换原始查询: {} -> {}", query.text(), optimizedQuery);
            
            // 创建新的查询对象，使用优化后的查询文本，但保留原始的元数据
            Query optimizedQueryObj = Query.from(optimizedQuery, query.metadata());
            
            return Collections.singletonList(optimizedQueryObj);
        } catch (Exception e) {
            log.error("查询转换失败，使用原始查询", e);
            return Collections.singletonList(query);
        }
    }
}
