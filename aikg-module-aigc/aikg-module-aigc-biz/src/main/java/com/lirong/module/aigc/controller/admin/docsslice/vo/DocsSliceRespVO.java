package com.lirong.module.aigc.controller.admin.docsslice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 文档切片 Response VO")
@Data
public class DocsSliceRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5430")
    private Long id;

    @Schema(description = "文档名称", example = "赵六")
    private String name;

    @Schema(description = "切片内容")
    private String content;

    @Schema(description = "字符数")
    private Integer wordNum;

    @Schema(description = "状态", example = "2")
    private Boolean status;

}