package com.lirong.module.aigc.core.service.impl;

import com.lirong.framework.common.util.spring.SpringUtils;
import com.lirong.module.aigc.core.functions.QueryOptimizerAI;
import com.lirong.module.aigc.core.service.QueryOptimizer;
import dev.langchain4j.model.localai.LocalAiChatModel;
import dev.langchain4j.service.AiServices;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * 查询优化服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class QueryOptimizerImpl implements QueryOptimizer {
    
    // 中文字符正则表达式
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]");
    // 英文字符正则表达式  
    private static final Pattern ENGLISH_PATTERN = Pattern.compile("[a-zA-Z]");
    
    @Override
    public String optimizeQuery(String originalQuery) {
        if (originalQuery == null || originalQuery.trim().isEmpty()) {
            return originalQuery;
        }
        
        try {
            log.info("开始优化查询: {}", originalQuery);
            
            // 检测语言
            String language = detectLanguage(originalQuery);
            log.info("检测到语言类型: {}", language);
            
            // 使用AI进行查询优化
            String optimizedQuery = optimizeQueryWithAI(originalQuery);
            
            // 如果是中文查询，可以考虑添加英文翻译来增强检索
            if ("zh".equals(language)) {
                String englishTranslation = translateText(originalQuery, "中文", "英文");
                if (englishTranslation != null && !englishTranslation.trim().isEmpty()) {
                    // 将英文翻译作为补充，但保持中文为主
                    optimizedQuery = optimizedQuery + " " + englishTranslation;
                    log.info("为中文查询添加英文翻译: {}", englishTranslation);
                }
            }
            
            log.info("查询优化完成: {} -> {}", originalQuery, optimizedQuery);
            return optimizedQuery;
            
        } catch (Exception e) {
            log.error("查询优化失败，返回原始查询: {}", originalQuery, e);
            return originalQuery;
        }
    }
    
    @Override
    public String detectLanguage(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "other";
        }
        
        try {
            // 首先使用简单的正则表达式进行快速检测
            boolean hasChinese = CHINESE_PATTERN.matcher(text).find();
            boolean hasEnglish = ENGLISH_PATTERN.matcher(text).find();
            
            if (hasChinese && !hasEnglish) {
                return "zh";
            } else if (hasEnglish && !hasChinese) {
                return "en";
            } else if (hasChinese && hasEnglish) {
                // 混合语言，判断哪种语言占主导
                long chineseCount = text.chars().filter(ch -> CHINESE_PATTERN.matcher(String.valueOf((char) ch)).matches()).count();
                long englishCount = text.chars().filter(ch -> ENGLISH_PATTERN.matcher(String.valueOf((char) ch)).matches()).count();
                return chineseCount > englishCount ? "zh" : "en";
            } else {
                // 使用AI进行更精确的语言检测
                return detectLanguageWithAI(text);
            }
        } catch (Exception e) {
            log.error("语言检测失败，返回默认值", e);
            return "other";
        }
    }
    
    @Override
    public String translateText(String text, String sourceLanguage, String targetLanguage) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }
        
        try {
            LocalAiChatModel.LocalAiChatModelBuilder builder = SpringUtils.getBean(LocalAiChatModel.LocalAiChatModelBuilder.class);
            LocalAiChatModel chatModel = builder.temperature(0.3).topP(0.8).build();
            
            QueryOptimizerAI queryOptimizerAI = AiServices.create(QueryOptimizerAI.class, chatModel);
            return queryOptimizerAI.translateText(text, sourceLanguage, targetLanguage);
        } catch (Exception e) {
            log.error("翻译失败: {} -> {}", sourceLanguage, targetLanguage, e);
            return text;
        }
    }
    
    /**
     * 使用AI优化查询
     */
    private String optimizeQueryWithAI(String query) {
        try {
            LocalAiChatModel.LocalAiChatModelBuilder builder = SpringUtils.getBean(LocalAiChatModel.LocalAiChatModelBuilder.class);
            LocalAiChatModel chatModel = builder.temperature(0.5).topP(0.8).build();
            
            QueryOptimizerAI queryOptimizerAI = AiServices.create(QueryOptimizerAI.class, chatModel);
            return queryOptimizerAI.optimizeQuery(query);
        } catch (Exception e) {
            log.error("AI查询优化失败", e);
            return query;
        }
    }
    
    /**
     * 使用AI检测语言
     */
    private String detectLanguageWithAI(String text) {
        try {
            LocalAiChatModel.LocalAiChatModelBuilder builder = SpringUtils.getBean(LocalAiChatModel.LocalAiChatModelBuilder.class);
            LocalAiChatModel chatModel = builder.temperature(0.1).topP(0.8).build();
            
            QueryOptimizerAI queryOptimizerAI = AiServices.create(QueryOptimizerAI.class, chatModel);
            return queryOptimizerAI.detectLanguage(text);
        } catch (Exception e) {
            log.error("AI语言检测失败", e);
            return "other";
        }
    }
}
