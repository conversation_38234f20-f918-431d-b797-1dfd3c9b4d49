package com.lirong.module.aigc.controller.admin.docsslice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 文档切片新增/修改 Request VO")
@Data
public class DocsSliceSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5430")
    private Long id;

    @Schema(description = "向量库的ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25506")
    @NotEmpty(message = "向量库的ID不能为空")
    private String vectorId;

    @Schema(description = "文档ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7517")
    @NotNull(message = "文档ID不能为空")
    private Long docsId;

    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6314")
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    @Schema(description = "文档名称", example = "赵六")
    private String name;

    @Schema(description = "切片内容")
    private String content;

    @Schema(description = "字符数")
    private Integer wordNum;

    @Schema(description = "状态", example = "2")
    private Boolean status;

    private String creator;

}