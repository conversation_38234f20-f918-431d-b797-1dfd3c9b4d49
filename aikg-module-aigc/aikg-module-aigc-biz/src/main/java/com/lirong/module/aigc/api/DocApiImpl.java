package com.lirong.module.aigc.api;

import com.lirong.framework.common.util.object.BeanUtils;
import com.lirong.module.aigc.api.dto.DocRespDTO;
import com.lirong.module.aigc.dal.dataobject.docs.DocsDO;
import com.lirong.module.aigc.service.docs.DocsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DocApiImpl implements DocApi{

    @Autowired
    private DocsService docsService;

    @Override
    public DocRespDTO getDoc(Long id) {
        DocsDO docsDO = docsService.getDocs(id);
        return BeanUtils.toBean(docsDO, DocRespDTO.class);
    }

}
