package com.lirong.module.portal.controller.admin.vo;

import lombok.Data;

@Data
public class QueryParam {

    /**
     * ES索引
     */
    private String esindex;
    /**
     * 关键词
     */
    private String keyword;
    /**
     * 停用词
     */
    private String stopword;
    /**
     * 开始时间
     */
    private String times;
    /**
     * 结束时间
     */
    private String timee;

    /**
     * 匹配模式，1-精准筛选，0-模糊筛选
     */
    private Integer matchingmode;
    /**
     * 排序方式，1-时间降序、2-时间升序、3-匹配度
     */
    private Integer sortType;

    private Integer page;

    private Integer size;


}
