<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lirong.boot</groupId>
        <artifactId>aikg-module-doc</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>aikg-module-doc-biz</artifactId>

    <repositories>
        <repository>
            <id>pdftron</id>
            <name>PDFNet Maven</name>
            <url>https://pdftron.com/maven/release</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-doc-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>

        <dependency>
            <groupId>co.elastic.clients</groupId>
            <artifactId>elasticsearch-java</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jfinal</groupId>
            <artifactId>activerecord</artifactId>
            <version>5.2.2</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.jfinal</groupId>-->
<!--            <artifactId>activerecord-plugin</artifactId>-->
<!--            <version>4.9.21</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.14</version>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>  <!-- 请根据需要使用最新版本 -->
        </dependency>

        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-pdf</artifactId>
            <version>24.12-crack</version>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j</artifactId>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-local-ai</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pdftron</groupId>
            <artifactId>PDFNet</artifactId>
            <version>10.10.0</version>
        </dependency>

        <!-- OpenCV -->
        <dependency>
            <groupId>org.openpnp</groupId>
            <artifactId>opencv</artifactId>
            <version>4.10.0-1.5.11</version>
        </dependency>

        <!-- Tesseract -->
        <dependency>
            <groupId>net.sourceforge.tess4j</groupId>
            <artifactId>tess4j</artifactId>
            <version>4.5.4</version>
        </dependency>

        <dependency>
            <groupId>cn.bigmodel.openapi</groupId>
            <artifactId>oapi-java-sdk</artifactId>
            <version>release-V4-2.3.2</version>
        </dependency>
        <dependency>
            <groupId>cn.bigmodel.openapi</groupId>
            <artifactId>oapi-java-sdk</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.16.5</version>
        </dependency>

        <dependency>
            <groupId>com.unfbx</groupId>
            <artifactId>chatgpt-java</artifactId>
            <version>1.0.14-beta1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.32</version>
        </dependency>
    </dependencies>

</project>