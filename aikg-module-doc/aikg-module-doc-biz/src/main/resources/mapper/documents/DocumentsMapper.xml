<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.module.doc.dal.mysql.documents.DocumentsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 结果映射 -->
    <resultMap id="DocumentsResultMap" type="com.lirong.module.doc.dal.dataobject.documents.DocumentsDO">
        <id property="id" column="id" />
        <result property="foreignTitle" column="foreign_title" />
        <result property="chineseTitle" column="chinese_title" />
        <result property="publishYear" column="publish_year" />
        <result property="publishMonth" column="publish_month" />
        <result property="publishDay" column="publish_day" />
        <result property="foreignAbstract" column="foreign_abstract" />
        <result property="chineseAbstract" column="chinese_abstract" />
        <result property="tags" column="tags" typeHandler="org.apache.ibatis.type.ArrayTypeHandler" />
        <result property="categoryId" column="category_id" />
        <result property="thumbnailUrl" column="thumbnail_url" />
        <result property="wordCount" column="word_count" />
        <result property="pageCount" column="page_count" />
        <result property="language" column="language" />
        <result property="originalUrl" column="original_url" />
        <result property="translationUrl" column="translation_url" />
        <result property="dataSource" column="data_source" />
        <result property="institutionId" column="institution_id" />
        <result property="institutionName" column="institution_name" />
        <result property="categoryName" column="category_name" />
        <collection property="authors" select="selectAuthorsById" column="id" />
    </resultMap>

    <select id="selectAuthorsById" resultType="java.lang.String">
        select de."name" from doc_document_experts dde
                                  inner join doc_experts de on dde.expert_id = de."id" and dde.deleted = 0 and de.deleted = 0
        where dde.document_id = #{id}
    </select>

    <!-- 分页查询 -->
    <select id="selectPage" resultMap="DocumentsResultMap">
        SELECT dd.*
             , di.name AS institution_name
             , dc.name as category_name
        FROM doc_documents dd
        LEFT JOIN doc_institutions di ON dd.institution_id = di.id
        left join doc_categories dc on dd.category_id = dc.id
        <where>
            <!-- 动态条件 -->
            <if test="reqVO.foreignTitle != null and reqVO.foreignTitle != ''">
                AND dd.foreign_title LIKE CONCAT('%', #{reqVO.foreignTitle}, '%')
            </if>
            <if test="reqVO.chineseTitle != null and reqVO.chineseTitle != ''">
                AND dd.chinese_title LIKE CONCAT('%', #{reqVO.chineseTitle}, '%')
            </if>
            <if test="reqVO.publishYear != null">
                AND dd.publish_year = #{reqVO.publishYear}
            </if>
            <!-- 处理 beginYear 和 endYear 的条件 -->
            <if test="reqVO.beginYear != null and reqVO.endYear != null">
                AND dd.publish_year BETWEEN #{reqVO.beginYear} AND #{reqVO.endYear}
            </if>
            <if test="reqVO.beginYear != null and reqVO.endYear == null">
                AND dd.publish_year &gt;= #{reqVO.beginYear}
            </if>
            <if test="reqVO.beginYear == null and reqVO.endYear != null">
                AND dd.publish_year &lt;= #{reqVO.endYear}
            </if>
            <if test="reqVO.institutionId != null">
                AND dd.institution_id = #{reqVO.institutionId}
            </if>
             <if test="reqVO.institutionName != null and reqVO.institutionName != ''">
                AND di.name LIKE CONCAT('%', #{reqVO.institutionName}, '%')
            </if>
            <if test="reqVO.categoryId != null">
                AND dd.category_id = #{reqVO.categoryId}
            </if>
            <if test="reqVO.syncStatus != null">
                AND dd.sync_status = #{reqVO.syncStatus}
            </if>
           <if test="reqVO.synchronize">
               and dd.publish_year is not null
           </if>
            <!-- 查询包含多个标签的记录, 完全匹配 -->
<!--            <if test="reqVO.tags != null and reqVO.tags.length > 0">-->
<!--                AND dd.tags @> ARRAY[-->
<!--                <foreach collection="reqVO.tags" item="tag" separator=",">-->
<!--                    #{tag}-->
<!--                </foreach>-->
<!--                ]::text[]-->
<!--            </if>-->

            <!-- 查询包含多个标签的记录, 模糊匹配 -->
            <if test="reqVO.tags != null and reqVO.tags.length > 0">
                AND (
                    <!-- 检查每个关键词是否至少匹配一个标签 -->
                    <foreach collection="reqVO.tags" item="tag" separator="AND">
                        EXISTS (
                            SELECT 1
                            FROM unnest(dd.tags) AS doc_tag
                            WHERE doc_tag LIKE CONCAT('%', #{tag}, '%')
                        )
                    </foreach>
                )
            </if>
        </where>
        ORDER BY dd.publish_year desc, dd.publish_month desc, dd.publish_day desc
    </select>

    <select id="selectBySources" resultMap="DocumentsResultMap">
        SELECT * FROM doc_documents
        <where>
            <if test='dataSources != null and dataSources.size() > 0'>
                AND data_source IN" +
                <foreach item='ds' index='index' collection='dataSources' open='(' separator=',' close=')'>
                    #{ds}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateSyncStatus">
        update doc_documents
            set sync_status = 1
          where id = #{id}
    </update>

    <update id="updateSyncStatusWithValue">
        update doc_documents
            set sync_status = #{status}
          where id = #{id}
    </update>

</mapper>