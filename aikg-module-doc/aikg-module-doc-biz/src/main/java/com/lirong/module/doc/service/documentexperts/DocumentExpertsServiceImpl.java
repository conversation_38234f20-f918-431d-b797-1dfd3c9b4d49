package com.lirong.module.doc.service.documentexperts;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.lirong.module.doc.controller.admin.documentexperts.vo.*;
import com.lirong.module.doc.dal.dataobject.documentexperts.DocumentExpertsDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.util.object.BeanUtils;

import com.lirong.module.doc.dal.mysql.documentexperts.DocumentExpertsMapper;

import javax.annotation.Resource;

import java.util.List;

import static com.lirong.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.lirong.module.doc.enums.ErrorCodeConstants.DOCUMENT_EXPERTS_NOT_EXISTS;

/**
 * 文献作者 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DocumentExpertsServiceImpl implements DocumentExpertsService {

    @Resource
    private DocumentExpertsMapper documentExpertsMapper;

    @Override
    public Long createDocumentExperts(DocumentExpertsSaveReqVO createReqVO) {
        // 插入
        DocumentExpertsDO documentExperts = BeanUtils.toBean(createReqVO, DocumentExpertsDO.class);
        documentExpertsMapper.insert(documentExperts);
        // 返回
        return documentExperts.getExpertId();
    }

    @Override
    public void deleteDocumentExperts(Long id) {
        // 校验存在
        validateumentExpertsExists(id);
        // 删除
        documentExpertsMapper.deleteById(id);
    }

    private void validateumentExpertsExists(Long id) {
        if (documentExpertsMapper.selectById(id) == null) {
            throw exception(DOCUMENT_EXPERTS_NOT_EXISTS);
        }
    }

    @Override
    public DocumentExpertsDO getDocumentExperts(Long id) {
        return documentExpertsMapper.selectById(id);
    }

//    @Override
//    public PageResult<DocumentExpertsDO> getDocumentExpertsPage(DocumentExpertsPageReqVO pageReqVO) {
//        return documentExpertsMapper.selectPage(pageReqVO);
//    }

    public void batchCreate(List<DocumentExpertsDO> batchAuthorList) {
        documentExpertsMapper.batchCreate(batchAuthorList);
    }

    public List<DocumentExpertsDO> selectByDocument(Long documentId) {
        return documentExpertsMapper.selectList(DocumentExpertsDO::getDocumentId, documentId);
    }

}