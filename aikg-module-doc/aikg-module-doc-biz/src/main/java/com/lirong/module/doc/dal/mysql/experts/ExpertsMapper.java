package com.lirong.module.doc.dal.mysql.experts;

import java.util.*;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.doc.dal.dataobject.experts.ExpertsDO;
import org.apache.ibatis.annotations.Mapper;
import com.lirong.module.doc.controller.admin.experts.vo.*;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.ArrayTypeHandler;

/**
 * 专家表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ExpertsMapper extends BaseMapperX<ExpertsDO> {

    default PageResult<ExpertsDO> selectPage(ExpertsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExpertsDO>()
                .likeIfPresent(ExpertsDO::getName, reqVO.getName())
                .eqIfPresent(ExpertsDO::getGender, reqVO.getGender())
                .eqIfPresent(ExpertsDO::getAvatarUrl, reqVO.getAvatarUrl())
                .eqIfPresent(ExpertsDO::getDescription, reqVO.getDescription())
                .eqIfPresent(ExpertsDO::getExpertiseFields, reqVO.getExpertiseFields())
                .eqIfPresent(ExpertsDO::getInstitutionId, reqVO.getInstitutionId())
                .betweenIfPresent(ExpertsDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(ExpertsDO::getId));
    }

    default ExpertsDO selectByName(String name, Long institutionId) {
      return selectOne(new LambdaQueryWrapperX<ExpertsDO>().eqIfPresent(ExpertsDO::getName, name).eqIfPresent(ExpertsDO::getInstitutionId, institutionId));
    }

    @Select("SELECT * FROM doc_experts WHERE id = #{id}")
    @Results({
            @Result(property = "expertiseFields", column = "expertise_fields", typeHandler = ArrayTypeHandler.class)
    })
    ExpertsDO selectById(Long id);

}