package com.lirong.module.doc.service.categories;

import java.util.*;
import com.lirong.module.doc.controller.admin.categories.vo.*;
import com.lirong.module.doc.dal.dataobject.categories.CategoriesDO;

import javax.validation.Valid;

/**
 * 文献分类表，支持多层分类 Service 接口
 *
 * <AUTHOR>
 */
public interface CategoriesService {

    /**
     * 创建文献分类表，支持多层分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategories(@Valid CategoriesSaveReqVO createReqVO);

    /**
     * 更新文献分类表，支持多层分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategories(@Valid CategoriesSaveReqVO updateReqVO);

    /**
     * 删除文献分类表，支持多层分类
     *
     * @param id 编号
     */
    void deleteCategories(Long id);

    /**
     * 获得文献分类表，支持多层分类
     *
     * @param id 编号
     * @return 文献分类表，支持多层分类
     */
    CategoriesDO getCategories(Long id);

    /**
     * 获得文献分类表，支持多层分类列表
     *
     * @param listReqVO 查询条件
     * @return 文献分类表，支持多层分类列表
     */
    List<CategoriesDO> getCategoriesList(CategoriesListReqVO listReqVO);

    /**
     * 获取叶子结点分类
     * @return
     */
    List<CategoriesDO> getLeafCategories();
}