package com.lirong;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import javax.imageio.ImageIO;

public class Screenshot {

    public static void main(String[] args) throws AWTException, InterruptedException, IOException {
        // 创建Robot实例
        Robot robot = new Robot();

        // 延迟一段时间，以确保屏幕已更新
        Thread.sleep(3000);

        // 获取屏幕尺寸
        Rectangle screenRect = new Rectangle(Toolkit.getDefaultToolkit().getScreenSize());
        int screenWidth = (int) screenRect.getWidth();
        int screenHeight = (int) screenRect.getHeight();

        // 计算每个部分的宽度和位置
        int sectionWidth = screenWidth / 3;

        // 截取左边的屏幕部分
        Rectangle leftRect = new Rectangle(0, 0, sectionWidth, screenHeight);
        BufferedImage leftScreenImage = robot.createScreenCapture(leftRect);
        ImageIO.write(leftScreenImage, "png", new File("screenshot_left.png"));

        // 截取中间的屏幕部分
        Rectangle middleRect = new Rectangle(sectionWidth, 0, sectionWidth, screenHeight);
        BufferedImage middleScreenImage = robot.createScreenCapture(middleRect);
        ImageIO.write(middleScreenImage, "png", new File("screenshot_middle.png"));

        // 截取右边的屏幕部分
        Rectangle rightRect = new Rectangle(sectionWidth * 2, 0, screenWidth - sectionWidth * 2, screenHeight);
        BufferedImage rightScreenImage = robot.createScreenCapture(rightRect);
        ImageIO.write(rightScreenImage, "png", new File("screenshot_right.png"));
    }

}
