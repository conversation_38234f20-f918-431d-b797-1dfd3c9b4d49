package com.lirong;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.druid.DruidPlugin;
import com.jfinal.plugin.activerecord.dialect.PostgreSqlDialect;
import com.lirong.domain.Institutions;
import org.junit.Test;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrgTest {

    @Test
    public void test() {

        DruidPlugin dp = new DruidPlugin("**********************************************", "aikg", "q1w2E#R$t5");
        ActiveRecordPlugin arp = new ActiveRecordPlugin(dp);
        arp.setDialect(new PostgreSqlDialect());
        arp.addMapping("doc_institutions", Institutions.class);
        arp.addMapping("doc_research_fields", ResearchField.class);
        arp.addMapping("doc_institution_research_fields", InstitutionResearchField.class);
        dp.start();
        arp.start();

        List<ResearchField> fields = new ResearchField().find("select * from doc_research_fields");
        Map<String, Long> fieldMap = new HashMap<>();
        fields.forEach(field -> {
            fieldMap.put(field.getStr("chinese_name"), field.getLong("id"));
        });

        // 创建HttpClient实例
        HttpClient httpClient = HttpClient.newHttpClient();

        // 定义请求的URL
        String url = "http://thinktank.ciraa.zju.edu.cn/apis/tte/org/list";

        // 定义请求体内容
        String requestBody = "{" +
                "\"pageIndex\":25," +
                "\"pageSize\":50," +
                "\"areaZh\":\"\"," +
                "\"countryZh\":\"\"," +
                "\"expertiseZh\":\"\"," +
                "\"key\":\"\"," +
                "\"hasRank\":0}";

        // 创建HttpRequest对象
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json") // 设置请求头
                .POST(HttpRequest.BodyPublishers.ofString(requestBody, StandardCharsets.UTF_8)) // 设置请求体
                .build();

        try {
            // 发送请求并获取响应
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            // 输出响应状态码和响应体
//            System.out.println("Response Code: " + response.statusCode());
//            System.out.println("Response Body: " + response.body());

            JSONObject result = JSON.parseObject(response.body());
            JSONArray datas = result.getJSONObject("data").getJSONArray("data");
            for (int i = 0; i < datas.size(); i++) {
                JSONObject data = datas.getJSONObject(i);
                String country = data.getString("countryZh");
                String orgName = data.getString("sourceName");
                String orgNameZh = data.getString("sourceNameZh");
                JSONArray expertiseZh = data.getJSONArray("expertiseZh");
                String host = data.getString("host");
                String remark = data.getString("remark");
                String remarkZh = data.getString("remarkZh");
                System.out.println(country + " " + orgName + " " + orgNameZh + " " + expertiseZh + " " + host + " " + remark + " " + remarkZh);

                Long orgId = IdUtil.getSnowflake().nextId();
                new Institutions()
                        .set("id", orgId)
                        .set("country", country)
                        .set("chinese_name", orgNameZh)
                        .set("name", orgName)
                        .set("description", remarkZh)
                        .set("official_website", host)
                        .set("create_time", new Date())
                        .set("update_time", new Date())
                        .save();

                expertiseZh.forEach(expert -> {
                    new InstitutionResearchField()
                            .set("institution_id", orgId)
                            .set("research_field_id", fieldMap.get(expert))
                            .set("create_time", new Date())
                            .set("update_time", new Date())
                            .save();
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
