package com.lirong;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class ExcelDoc {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("英文标题")
    private String foreignTitle;

    @ExcelProperty("发布日期")
    private String publishDate;

    @ExcelProperty("英文摘要")
    private String foreignAbstract;

    @ExcelProperty("所属机构网址")
    private String website;

    @ExcelProperty("所属机构")
    private String publisher;

    @ExcelProperty("pdf来源")
    private String originalUrl;

    @ExcelProperty("来源")
    private String dataSource;

}
