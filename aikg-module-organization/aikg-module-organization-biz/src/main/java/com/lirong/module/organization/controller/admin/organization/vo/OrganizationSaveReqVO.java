package com.lirong.module.organization.controller.admin.organization.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 机构新增/修改 Request VO")
@Data
public class OrganizationSaveReqVO {

    @Schema(description = "机构编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11346")
    private Long id;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "中文名称不能为空")
    private String nameCn;

    @Schema(description = "徽章")
    private String badge;

    @Schema(description = "机构类型", example = "2")
    private String type;

    @Schema(description = "机构简介")
    private String introduction;

    @Schema(description = "官网")
    private String website;

}