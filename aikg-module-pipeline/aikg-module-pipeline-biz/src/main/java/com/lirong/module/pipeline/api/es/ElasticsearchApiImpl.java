package com.lirong.module.pipeline.api.es;

import com.lirong.module.pipeline.api.es.dto.BaseMessage;
import com.lirong.module.pipeline.enums.Const;
import com.lirong.module.pipeline.service.ElasticSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ElasticsearchApiImpl implements ElasticsearchApi{

    @Autowired
    private ElasticSearchService esService;

    /**
     * 保存新闻
     * @param message
     * @return
     */
    public boolean saveNews(BaseMessage message) {
       return esService.save(Const.NEWS_INDEX, message);
    }

}
