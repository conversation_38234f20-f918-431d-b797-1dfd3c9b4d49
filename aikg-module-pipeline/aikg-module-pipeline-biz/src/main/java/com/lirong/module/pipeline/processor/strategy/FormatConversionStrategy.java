package com.lirong.module.pipeline.processor.strategy;

import com.lirong.module.pipeline.api.es.dto.NewsMessage;
import com.lirong.module.pipeline.processor.NewsProcessingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * 格式转换
 */
@Component
@Order(3)
@Slf4j
public class FormatConversionStrategy implements NewsProcessingStrategy {

    @Override
    public CompletableFuture<NewsMessage> processAsync(NewsMessage message) {
        return CompletableFuture.supplyAsync(() -> {
            message.setPublishDate(message.getPublishDate());
            return message;
        });
    }

    /**
     * 日期格式转换
     * @param inputDate
     * @return
     */
    private Date parseDate(String inputDate) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("M/d/yyyy");

        try {
            return inputFormat.parse(inputDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

}
