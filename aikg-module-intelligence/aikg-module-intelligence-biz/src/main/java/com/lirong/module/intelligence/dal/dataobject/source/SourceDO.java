package com.lirong.module.intelligence.dal.dataobject.source;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.lirong.framework.mybatis.core.dataobject.BaseDO;

/**
 * 采集源 DO
 *
 * <AUTHOR>
 */
@TableName("intelligence_source")
@KeySequence("intelligence_source_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 国家
     */
    private String country;
    /**
     * 简称
     */
    private String shortName;
    /**
     * 名称（中文）
     */
    private String sourceName;
    /**
     * 名称（英文）
     */
    private String sourceNameEn;
    /**
     * 网站类型
     */
    private String type;
    /**
     * 网站logo
     */
    private String logo;
    /**
     * 域名
     */
    private String domain;
    /**
     * 简介
     */
    private String introduction;

}