package com.lirong.module.intelligence.controller.admin.source.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.lirong.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.lirong.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 采集源分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SourcePageReqVO extends PageParam {

    @Schema(description = "国家")
    private String country;

    @Schema(description = "简称", example = "张三")
    private String shortName;

    @Schema(description = "名称（中文）", example = "王五")
    private String sourceName;

    @Schema(description = "名称（英文）")
    private String sourceNameEn;

    @Schema(description = "网站类型", example = "1")
    private String type;

    @Schema(description = "网站logo")
    private String logo;

    @Schema(description = "域名")
    private String domain;

    @Schema(description = "简介")
    private String introduction;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}